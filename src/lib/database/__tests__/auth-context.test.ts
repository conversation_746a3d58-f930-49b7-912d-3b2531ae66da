import { describe, it, expect } from 'vitest';
import { validateClinicAccess, getServiceContext } from '../auth-context';
import type { AuthContext } from '../auth-context';

describe('Auth Context', () => {
  describe('validateClinicAccess', () => {
    it('should return true for specific clinic access', () => {
      const authContext: AuthContext = {
        userId: 'user123',
        authId: 'auth123',
        clinicIds: ['clinic1', 'clinic2'],
        currentClinicId: 'clinic1',
        role: 'provider',
      };

      expect(validateClinicAccess(authContext, 'clinic1')).toBe(true);
      expect(validateClinicAccess(authContext, 'clinic2')).toBe(true);
    });

    it('should return false for clinic without access', () => {
      const authContext: AuthContext = {
        userId: 'user123',
        authId: 'auth123',
        clinicIds: ['clinic1', 'clinic2'],
        currentClinicId: 'clinic1',
        role: 'provider',
      };

      expect(validateClinicAccess(authContext, 'clinic3')).toBe(false);
      expect(validateClinicAccess(authContext, 'nonexistent')).toBe(false);
    });

    it('should return true for wildcard access', () => {
      const authContext: AuthContext = {
        userId: 'system',
        authId: 'system',
        clinicIds: ['*'],
        role: 'system',
        isSystemAdmin: true,
      };

      expect(validateClinicAccess(authContext, 'clinic1')).toBe(true);
      expect(validateClinicAccess(authContext, 'clinic2')).toBe(true);
      expect(validateClinicAccess(authContext, 'any-clinic-id')).toBe(true);
      expect(validateClinicAccess(authContext, 'nonexistent')).toBe(true);
    });

    it('should return true for wildcard access even with other clinic IDs', () => {
      const authContext: AuthContext = {
        userId: 'system',
        authId: 'system',
        clinicIds: ['*', 'clinic1', 'clinic2'],
        role: 'system',
        isSystemAdmin: true,
      };

      expect(validateClinicAccess(authContext, 'clinic1')).toBe(true);
      expect(validateClinicAccess(authContext, 'clinic3')).toBe(true);
      expect(validateClinicAccess(authContext, 'any-clinic-id')).toBe(true);
    });

    it('should handle empty clinic IDs array', () => {
      const authContext: AuthContext = {
        userId: 'user123',
        authId: 'auth123',
        clinicIds: [],
        currentClinicId: undefined,
        role: 'provider',
      };

      expect(validateClinicAccess(authContext, 'clinic1')).toBe(false);
      expect(validateClinicAccess(authContext, 'any-clinic')).toBe(false);
    });
  });

  describe('getServiceContext', () => {
    it('should return service context with wildcard access', () => {
      const serviceContext = getServiceContext();

      expect(serviceContext.userId).toBe('system');
      expect(serviceContext.authId).toBe('system');
      expect(serviceContext.clinicIds).toEqual(['*']);
      expect(serviceContext.role).toBe('system');
      expect(serviceContext.isSystemAdmin).toBe(true);
    });

    it('should allow access to any clinic through validateClinicAccess', () => {
      const serviceContext = getServiceContext();

      expect(validateClinicAccess(serviceContext, 'clinic1')).toBe(true);
      expect(validateClinicAccess(serviceContext, 'clinic2')).toBe(true);
      expect(validateClinicAccess(serviceContext, 'any-clinic-id')).toBe(true);
    });
  });
});
